{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?8648", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?10fd", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?5161", "uni-app:///pages/activation/activation.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?ca6a", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?4d62"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "inputToken", "userQuota", "baseLimit", "activityLimit", "tempLimit", "totalLimit", "currentUseNum", "remainingLimit", "<PERSON><PERSON><PERSON><PERSON>", "newToken", "addTokenFlag", "tokenName", "showTokenDialog", "showDonateDialog", "computed", "todayUsage", "remainingTotalQuota", "onLoad", "methods", "initPage", "tokenApi", "tokenRes", "console", "verifyToken", "uni", "title", "icon", "duration", "res", "tokenTime", "tokenStatus", "tokenCompCode", "detailRes", "tokenDetail", "addToken", "time", "nowTime", "handleTokenDialogClose", "refresh<PERSON><PERSON><PERSON>", "logout", "content", "success", "copyText", "utils", "openDonateDialog", "closeDonateDialog", "goToSignIn", "url", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4rB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqNhtB;AAIA;AAEA;AAAA;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMAP;cAAA;gBAAAQ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAJ;;gBAEA;gBACA;kBACAb;kBACAkB;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACA7B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAY;cAAA;gBAAAY;gBACA;kBACAC;kBACA,kDACA,kBACAA,YACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;cAAA;gBAGAE;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAEAH;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACAC,gCACA;gBACAzB;gBAAA;gBAAA,OAEAS;cAAA;gBAAAQ;gBACA;kBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAJ;gBACA;kBACAA;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAlB;cAAA;gBAAAQ;gBACA;kBACA;kBACAJ;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MACAf;QACAC;QACAe;QACAC;UACA;YACAjB;YACA;YACA;cACAtB;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACA;YACA;YAEAgB;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAe;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAtB;QACAuB;MACA;IACA;IAEA;IACAC;MACAxB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1eA;AAAA;AAAA;AAAA;AAAygC,CAAgB,m+BAAG,EAAC,C;;;;;;;;;;;ACA7hC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activation/activation.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activation/activation.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./activation.vue?vue&type=template&id=4f5b6586&scoped=true&\"\nvar renderjs\nimport script from \"./activation.vue?vue&type=script&lang=js&\"\nexport * from \"./activation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f5b6586\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activation/activation.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=template&id=4f5b6586&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"header-title\">激活码管理</view>\r\n\t\t\t<view class=\"header-subtitle\">获取激活码与额度查询</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 激活码管理卡片 -->\r\n\t\t<view class=\"card fade-in\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<view class=\"card-title\">我的激活码</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card-content\">\r\n\t\t\t\t<!-- 未登录状态 -->\r\n\t\t\t\t<view v-if=\"!userInfo\" class=\"token-input-section\">\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<input v-model=\"inputToken\" placeholder=\"请输入激活码\" class=\"token-input\" maxlength=\"50\" />\r\n\t\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"verifyToken\">验证激活码</button>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 获取激活码 -->\r\n\t\t\t\t\t<view v-if=\"!addTokenFlag\" class=\"get-token-section\">\r\n\t\t\t\t\t\t<view class=\"divider\">或</view>\r\n\t\t\t\t\t\t<input v-model=\"newToken\" placeholder=\"点击按钮获取激活码\" class=\"token-input\" disabled />\r\n\t\t\t\t\t\t<button class=\"btn btn-success\" @click=\"addToken\">获取激活码</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 已登录状态 -->\r\n\t\t\t\t<view v-else class=\"token-info-section\">\r\n\t\t\t\t\t<!-- 警告提示 -->\r\n\t\t\t\t\t<view class=\"alert alert-warning\">\r\n\t\t\t\t\t\t<view class=\"alert-icon\">⚠️</view>\r\n\t\t\t\t\t\t<view class=\"alert-text\">请妥善保管您的激活码，请勿泄露给他人</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 激活码信息 -->\r\n\t\t\t\t\t<view class=\"info-list\">\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<view class=\"info-label\">激活码:</view>\r\n\t\t\t\t\t\t\t<view class=\"info-value-group\">\r\n\t\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenName }}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"copy-btn\" @click=\"copyText(userInfo.tokenName)\">📋</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<view class=\"info-label\">绑定设备:</view>\r\n\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenCompCode || '未绑定设备' }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<view class=\"info-label\">创建时间:</view>\r\n\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenTime }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<view class=\"info-label\">账户状态:</view>\r\n\t\t\t\t\t\t\t<view class=\"tag\" :class=\"userInfo.tokenStatus === 0 ? 'tag-success' : 'tag-danger'\">\r\n\t\t\t\t\t\t\t\t{{ userInfo.tokenStatus === 0 ? '正常' : '已禁用' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 额度管理卡片 -->\r\n\t\t<view v-if=\"userInfo\" class=\"card fade-in\" style=\"animation-delay: 0.1s;\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<view class=\"card-title\">额度使用情况</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card-content\">\r\n\t\t\t\t<!-- 主要额度显示 -->\r\n\t\t\t\t<view class=\"quota-main\">\r\n\t\t\t\t\t<view class=\"quota-card today-usage\">\r\n\t\t\t\t\t\t<view class=\"quota-icon\">📅</view>\r\n\t\t\t\t\t\t<view class=\"quota-content\">\r\n\t\t\t\t\t\t\t<view class=\"quota-value\">{{ todayUsage || 0 }}</view>\r\n\t\t\t\t\t\t\t<view class=\"quota-label\">今日使用额度</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"quota-card remaining-total\">\r\n\t\t\t\t\t\t<view class=\"quota-icon\">💰</view>\r\n\t\t\t\t\t\t<view class=\"quota-content\">\r\n\t\t\t\t\t\t\t<view class=\"quota-value\">{{ remainingTotalQuota || 0 }}</view>\r\n\t\t\t\t\t\t\t<view class=\"quota-label\">剩余总额度</view>\r\n\t\t\t\t\t\t\t<view class=\"quota-sublabel\">基础+签到+活动+永久</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 详细额度信息 -->\r\n\t\t\t\t<view class=\"quota-details\">\r\n\t\t\t\t\t<view class=\"quota-detail-item\">\r\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.baseLimit || 0 }}</view>\r\n\t\t\t\t\t\t<view class=\"quota-detail-label\">基础额度</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"quota-detail-item\">\r\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.activityLimit || 0 }}</view>\r\n\t\t\t\t\t\t<view class=\"quota-detail-label\">活动额度</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"quota-detail-item\">\r\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.tempLimit || 0 }}</view>\r\n\t\t\t\t\t\t<view class=\"quota-detail-label\">签到额度</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"quota-detail-item\">\r\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.permanentQuota || 0 }}</view>\r\n\t\t\t\t\t\t<view class=\"quota-detail-label\">永久额度</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 永久额度提示 -->\r\n\t\t\t\t<view class=\"alert alert-warning\">\r\n\t\t\t\t\t<view class=\"alert-icon\">⚠️</view>\r\n\t\t\t\t\t<view class=\"alert-text\">永久额度是一次性消耗品，不会刷新重置！永久额度只有在其他额度用完后才会使用！</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 操作按钮 -->\r\n\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t<button class=\"btn btn-success\" @click=\"refreshQuota\">刷新额度</button>\r\n\t\t\t\t\t<button class=\"btn btn-danger\" @click=\"logout\">重置激活码</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 导航按钮 -->\r\n\t\t<view class=\"nav-buttons\">\r\n\t\t\t<button class=\"btn btn-primary nav-btn\" @click=\"goToSignIn\">前往签到</button>\r\n\t\t\t<button class=\"btn btn-secondary nav-btn\" @click=\"goBack\">返回首页</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 激活码获取成功弹窗 -->\r\n\t\t<view v-if=\"showTokenDialog\" class=\"modal-overlay\" @click=\"handleTokenDialogClose\">\r\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<view class=\"modal-title\">激活码获取成功</view>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"handleTokenDialogClose\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"token-dialog-content\">\r\n\t\t\t\t\t<view class=\"token-dialog-tip\">\r\n\t\t\t\t\t\t<text>⚠️ 请妥善保管您的激活码，请勿泄露给他人</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"token-dialog-code\">\r\n\t\t\t\t\t\t<view class=\"token-dialog-label\">您的激活码:</view>\r\n\t\t\t\t\t\t<view class=\"token-dialog-value-group\">\r\n\t\t\t\t\t\t\t<view class=\"token-dialog-value\">{{ newToken }}</view>\r\n\t\t\t\t\t\t\t<view class=\"copy-btn\" @click=\"copyText(newToken)\">📋</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"token-dialog-steps\">\r\n\t\t\t\t\t\t<view class=\"step-item completed\">\r\n\t\t\t\t\t\t\t<view class=\"step-number\">1</view>\r\n\t\t\t\t\t\t\t<view class=\"step-text\">已获取激活码</view>\r\n\t\t\t\t\t\t\t<view class=\"step-status\">✅</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t\t\t<view class=\"step-number\">2</view>\r\n\t\t\t\t\t\t\t<view class=\"step-text\">去小说下载器中激活该激活码</view>\r\n\t\t\t\t\t\t\t<view class=\"step-status\">➡️</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"step-item\">\r\n\t\t\t\t\t\t\t<view class=\"step-number\">3</view>\r\n\t\t\t\t\t\t\t<view class=\"step-text\">回到此页面，验证激活码</view>\r\n\t\t\t\t\t\t\t<view class=\"step-status\">➡️</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"handleTokenDialogClose\">确定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 赞助弹窗 -->\r\n\t\t<view v-if=\"showDonateDialog\" class=\"modal-overlay\" @click=\"closeDonateDialog\">\r\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<view class=\"modal-title\">支持我们的开发</view>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeDonateDialog\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"donate-dialog-content\">\r\n\t\t\t\t\t<view class=\"donate-header\">\r\n\t\t\t\t\t\t<view class=\"donate-title\">感谢您的支持</view>\r\n\t\t\t\t\t\t<view class=\"donate-description\">\r\n\t\t\t\t\t\t\t您的支持是我们持续改进的动力。如果您觉得我们的工具对您有所帮助，可以考虑给予我们一些赞助。\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"donate-methods\">\r\n\t\t\t\t\t\t<view class=\"donate-method\">\r\n\t\t\t\t\t\t\t<view class=\"method-title\">支付宝</view>\r\n\t\t\t\t\t\t\t<view class=\"qr-placeholder\">支付宝收款码</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"donate-method\">\r\n\t\t\t\t\t\t\t<view class=\"method-title\">微信支付</view>\r\n\t\t\t\t\t\t\t<view class=\"qr-placeholder\">微信收款码</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"donate-footer\">\r\n\t\t\t\t\t\t<view class=\"donate-tip\">赞助为自愿行为，不影响软件的正常使用</view>\r\n\t\t\t\t\t\t<view class=\"donate-tip\">赞助后请留下您的联系方式或者激活码，我们将回馈您一定额度支持</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"btn btn-success\" @click=\"closeDonateDialog\">关闭</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\ttokenApi,\r\n\t\tutils\r\n\t} from '@/api/index'\r\n\timport {\r\n\t\tcheckLogin\r\n\t} from '@/utils/auth'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用户信息\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tinputToken: '',\r\n\t\t\t\tuserQuota: {\r\n\t\t\t\t\tbaseLimit: 0,\r\n\t\t\t\t\tactivityLimit: 0,\r\n\t\t\t\t\ttempLimit: 0,\r\n\t\t\t\t\ttotalLimit: 0,\r\n\t\t\t\t\tcurrentUseNum: 0,\r\n\t\t\t\t\tremainingLimit: 0,\r\n\t\t\t\t\tpermanentQuota: 0\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 激活码相关\r\n\t\t\t\tnewToken: '',\r\n\t\t\t\taddTokenFlag: true,\r\n\t\t\t\ttokenName: '',\r\n\r\n\t\t\t\t// 弹窗状态\r\n\t\t\t\tshowTokenDialog: false,\r\n\t\t\t\tshowDonateDialog: false\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\ttodayUsage() {\r\n\t\t\t\treturn this.userQuota.currentUseNum || 0\r\n\t\t\t},\r\n\t\t\tremainingTotalQuota() {\r\n\t\t\t\tconst totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)\r\n\t\t\t\tconst used = this.userQuota.currentUseNum || 0\r\n\t\t\t\treturn Math.max(0, totalQuota - used)\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 检查登录状态\r\n\t\t\tif (!checkLogin()) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.initPage()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 初始化页面\r\n\t\t\tasync initPage() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取用户token状态\r\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\r\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\r\n\t\t\t\t\t\tthis.addTokenFlag = true\r\n\t\t\t\t\t\tthis.tokenName = tokenRes.data.tokenName\r\n\t\t\t\t\t\tif (this.tokenName) {\r\n\t\t\t\t\t\t\tthis.inputToken = this.tokenName\r\n\t\t\t\t\t\t\tawait this.verifyToken()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.addTokenFlag = false\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取用户token失败:', error)\r\n\t\t\t\t\tthis.addTokenFlag = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 验证激活码\r\n\t\t\tasync verifyToken() {\r\n\t\t\t\tif (!this.inputToken) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入激活码',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取激活码信息\r\n\t\t\t\t\tconst res = await tokenApi.getQuota(this.inputToken)\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t// 保存激活码到本地存储\r\n\t\t\t\t\t\tuni.setStorageSync('fq_token', this.inputToken)\r\n\r\n\t\t\t\t\t\t// 设置用户信息\r\n\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\ttokenName: this.inputToken,\r\n\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\r\n\t\t\t\t\t\t\ttokenStatus: 0,\r\n\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 保存额度信息\r\n\t\t\t\t\t\tthis.userQuota = res.data || {\r\n\t\t\t\t\t\t\tbaseLimit: 0,\r\n\t\t\t\t\t\t\tactivityLimit: 0,\r\n\t\t\t\t\t\t\ttempLimit: 0,\r\n\t\t\t\t\t\t\ttotalLimit: 0,\r\n\t\t\t\t\t\t\tcurrentUseNum: 0,\r\n\t\t\t\t\t\t\tremainingLimit: 0,\r\n\t\t\t\t\t\t\tpermanentQuota: 0\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 尝试获取额外的token信息\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst detailRes = await tokenApi.getTokenInfo(this.inputToken)\r\n\t\t\t\t\t\t\tif (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {\r\n\t\t\t\t\t\t\t\tconst tokenDetail = detailRes.rows[0]\r\n\t\t\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\t\t\t...this.userInfo,\r\n\t\t\t\t\t\t\t\t\t...tokenDetail\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('获取token详情失败:', error)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '激活码验证成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '激活码无效',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('验证激活码失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '验证激活码失败，请稍后再试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取激活码\r\n\t\t\tasync addToken() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst time = utils.getCurrentTimeString()\r\n\t\t\t\t\tconst nowTime = new Date().getTime()\r\n\t\t\t\t\t// 简化的激活码生成，实际项目中建议使用更安全的方法\r\n\t\t\t\t\tconst tokenName = utils.encrypt('huswhusbg', 'wqowwjnsm', nowTime.toString())\r\n\r\n\t\t\t\t\tconst res = await tokenApi.addToken(tokenName, time)\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.newToken = tokenName\r\n\t\t\t\t\t\tthis.inputToken = tokenName\r\n\r\n\t\t\t\t\t\t// 显示弹窗\r\n\t\t\t\t\t\tthis.showTokenDialog = true\r\n\r\n\t\t\t\t\t\t// 存储激活码\r\n\t\t\t\t\t\tuni.setStorageSync('fq_token', this.inputToken)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '获取激活码失败',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取激活码失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取激活码失败，请稍后再试',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 激活码弹窗关闭处理\r\n\t\t\tasync handleTokenDialogClose() {\r\n\t\t\t\tthis.showTokenDialog = false\r\n\t\t\t\tthis.addTokenFlag = true\r\n\t\t\t\tawait this.verifyToken()\r\n\t\t\t},\r\n\r\n\t\t\t// 刷新额度\r\n\t\t\tasync refreshQuota() {\r\n\t\t\t\tif (!this.userInfo) return\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await tokenApi.refreshQuota(this.userInfo.tokenName)\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.userQuota = res.data\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '额度刷新成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('刷新额度失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '刷新额度失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 重置激活码\r\n\t\t\tlogout() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '确定要重置激活码吗？重置激活码不会重置额度，该按钮旨在帮助遇见bug用户清空当前的激活码，一般情况不要点！',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.removeStorageSync('fq_token')\r\n\t\t\t\t\t\t\tthis.userInfo = null\r\n\t\t\t\t\t\t\tthis.userQuota = {\r\n\t\t\t\t\t\t\t\tbaseLimit: 0,\r\n\t\t\t\t\t\t\t\tactivityLimit: 0,\r\n\t\t\t\t\t\t\t\ttempLimit: 0,\r\n\t\t\t\t\t\t\t\ttotalLimit: 0,\r\n\t\t\t\t\t\t\t\tcurrentUseNum: 0,\r\n\t\t\t\t\t\t\t\tremainingLimit: 0,\r\n\t\t\t\t\t\t\t\tpermanentQuota: 0\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.inputToken = ''\r\n\t\t\t\t\t\t\tthis.addTokenFlag = false\r\n\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '已重置激活码',\r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 复制文本\r\n\t\t\tasync copyText(text) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait utils.copyText(text)\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('复制失败:', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开赞助弹窗\r\n\t\t\topenDonateDialog() {\r\n\t\t\t\tthis.showDonateDialog = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭赞助弹窗\r\n\t\t\tcloseDonateDialog() {\r\n\t\t\t\tthis.showDonateDialog = false\r\n\t\t\t},\r\n\r\n\t\t\t// 前往签到页面\r\n\t\t\tgoToSignIn() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/signin/signin'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 返回首页\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t/* 页面容器 */\r\n\t.container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t/* 页面头部 */\r\n\t.header {\r\n\t\ttext-align: center;\r\n\t\tpadding: 40rpx 0;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.header-subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t\tmargin-bottom: 30rpx;\r\n\t\toverflow: hidden;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.card:hover {\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.fade-in {\r\n\t\tanimation: fadeInUp 0.6s ease-out;\r\n\t}\r\n\r\n\t@keyframes fadeInUp {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(30rpx);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.card-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\r\n\t}\r\n\r\n\t.card-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.card-content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t/* 标签样式 */\r\n\t.tag {\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.tag-success {\r\n\t\tbackground-color: #f0f9eb;\r\n\t\tcolor: #67C23A;\r\n\t\tborder: 1rpx solid #c2e7b0;\r\n\t}\r\n\r\n\t.tag-danger {\r\n\t\tbackground-color: #fef0f0;\r\n\t\tcolor: #F56C6C;\r\n\t\tborder: 1rpx solid #fbc4c4;\r\n\t}\r\n\r\n\t.tag-info {\r\n\t\tbackground-color: #f4f4f5;\r\n\t\tcolor: #909399;\r\n\t\tborder: 1rpx solid #d3d4d6;\r\n\t}\r\n\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tborder-radius: 25rpx;\r\n\t\tborder: none;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttext-align: center;\r\n\t\tdisplay: inline-block;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.btn-primary {\r\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-success {\r\n\t\tbackground: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-danger {\r\n\t\tbackground: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-warning {\r\n\t\tbackground: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-secondary {\r\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.btn-disabled {\r\n\t\tbackground-color: #c0c4cc;\r\n\t\tcolor: #ffffff;\r\n\t\tcursor: not-allowed;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.btn-disabled:hover {\r\n\t\ttransform: none;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t/* 激活码输入区域 */\r\n\t.token-input-section {\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.input-group {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.token-input {\r\n\t\theight: 80rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tborder: 2rpx solid #e0e0e0;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.token-input:focus {\r\n\t\tborder-color: #409EFF;\r\n\t\tbox-shadow: 0 0 0 4rpx rgba(64, 158, 255, 0.1);\r\n\t}\r\n\r\n\t.get-token-section {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.divider {\r\n\t\tmargin: 30rpx 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.divider::before,\r\n\t.divider::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\twidth: 30%;\r\n\t\theight: 1rpx;\r\n\t\tbackground-color: #e0e0e0;\r\n\t}\r\n\r\n\t.divider::before {\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.divider::after {\r\n\t\tright: 0;\r\n\t}\r\n\r\n\t/* 激活码信息区域 */\r\n\t.token-info-section {\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.alert {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.alert-warning {\r\n\t\tbackground-color: #fdf6ec;\r\n\t\tborder: 1rpx solid #f5dab1;\r\n\t}\r\n\r\n\t.alert-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.alert-text {\r\n\t\tflex: 1;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #E6A23C;\r\n\t}\r\n\r\n\t.info-list {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.info-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.info-label {\r\n\t\twidth: 150rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.info-value {\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.info-value-group {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.copy-btn {\r\n\t\tpadding: 10rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #409EFF;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t/* 额度管理样式 */\r\n\t.quota-main {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.quota-card {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx 20rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.quota-card:hover {\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.today-usage {\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.remaining-total {\r\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.quota-icon {\r\n\t\tfont-size: 48rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.quota-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.quota-value {\r\n\t\tfont-size: 42rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.quota-label {\r\n\t\tfont-size: 24rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t.quota-sublabel {\r\n\t\tfont-size: 20rpx;\r\n\t\topacity: 0.7;\r\n\t\tmargin-top: 4rpx;\r\n\t}\r\n\r\n\t/* 详细额度信息 */\r\n\t.quota-details {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.quota-detail-item {\r\n\t\ttext-align: center;\r\n\t\tpadding: 25rpx 15rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder: 1rpx solid #e9ecef;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.quota-detail-item:hover {\r\n\t\tbackground-color: #e3f2fd;\r\n\t\tborder-color: #409EFF;\r\n\t\ttransform: translateY(-2rpx);\r\n\t}\r\n\r\n\t.quota-detail-value {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #409EFF;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.quota-detail-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 操作按钮 */\r\n\t.action-buttons {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tgap: 15rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.action-buttons .btn {\r\n\t\tflex: 1;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 35rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t/* 导航按钮 */\r\n\t.nav-buttons {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t/* 弹窗样式 */\r\n\t.modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.modal-content {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\twidth: 90%;\r\n\t\tmax-width: 600rpx;\r\n\t\tmax-height: 80vh;\r\n\t\toverflow-y: auto;\r\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);\r\n\t\tanimation: modalSlideIn 0.3s ease-out;\r\n\t}\r\n\r\n\t@keyframes modalSlideIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttransform: translateY(-50rpx) scale(0.9);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateY(0) scale(1);\r\n\t\t}\r\n\t}\r\n\r\n\t.modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.modal-close {\r\n\t\tfont-size: 48rpx;\r\n\t\tcolor: #999999;\r\n\t\tcursor: pointer;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 50%;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.modal-close:hover {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.modal-footer {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t/* 激活码弹窗样式 */\r\n\t.token-dialog-content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.token-dialog-tip {\r\n\t\tbackground-color: #fdf6ec;\r\n\t\tcolor: #E6A23C;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tborder-left: 6rpx solid #E6A23C;\r\n\t}\r\n\r\n\t.token-dialog-code {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbackground-color: #f0f9eb;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder-left: 6rpx solid #67C23A;\r\n\t}\r\n\r\n\t.token-dialog-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.token-dialog-value-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 15rpx;\r\n\t}\r\n\r\n\t.token-dialog-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #67C23A;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t.token-dialog-steps {\r\n\t\tmargin-top: 30rpx;\r\n\t\ttext-align: left;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.step-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-bottom: 1rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.step-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.step-item.completed {\r\n\t\tcolor: #67C23A;\r\n\t}\r\n\r\n\t.step-number {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #ffffff;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-right: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.step-item.completed .step-number {\r\n\t\tbackground-color: #67C23A;\r\n\t}\r\n\r\n\t.step-text {\r\n\t\tflex: 1;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.step-status {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t/* 赞助弹窗样式 */\r\n\t.donate-dialog-content {\r\n\t\tpadding: 30rpx 20rpx;\r\n\t}\r\n\r\n\t.donate-header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.donate-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.donate-description {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tline-height: 1.6;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.donate-methods {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tmargin: 30rpx 0;\r\n\t\tgap: 30rpx;\r\n\t}\r\n\r\n\t.donate-method {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.method-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.qr-placeholder {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.donate-footer {\r\n\t\tborder-top: 1rpx solid #e0e0e0;\r\n\t\tpadding-top: 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.donate-tip {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tmargin: 10rpx 0;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media screen and (max-width: 750rpx) {\r\n\t\t.quota-main {\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.quota-details {\r\n\t\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\t}\r\n\r\n\t\t.action-buttons {\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.donate-methods {\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n\r\n\t@media screen and (max-width: 600rpx) {\r\n\t\t.quota-details {\r\n\t\t\tgrid-template-columns: 1fr;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755084286212\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}